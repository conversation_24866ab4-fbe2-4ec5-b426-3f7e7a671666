#!/bin/bash

# Display what's happening
echo "Starting Interview Coder Electron App..."

# Ensure we're in the right directory
cd "$(dirname "$0")"

# Kill any existing Electron processes
echo "Cleaning up any existing Electron processes..."
pkill -f "electron" 2>/dev/null || true
sleep 1

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed or not in PATH"
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ] || [ ! -d "node_modules/electron" ]; then
    echo "Dependencies not found. Please run 'bash build.sh' first."
    exit 1
fi

# Check if frontend is running
echo "Checking if frontend server is running on http://localhost:5173..."
if ! curl -s http://localhost:5173 > /dev/null; then
    echo "Warning: Frontend server doesn't seem to be running on http://localhost:5173"
    echo "Please start the frontend first by running 'bash start-frontend.sh' in another terminal."
    echo ""
    echo "Waiting for frontend server to become available..."
fi

# Start the Electron app
echo "Starting the Electron desktop application..."
echo "This will open the desktop window that loads the React app from the frontend server."
echo ""
npm run electron:start
