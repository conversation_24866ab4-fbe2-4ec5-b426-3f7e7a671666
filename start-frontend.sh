#!/bin/bash

# Display what's happening
echo "Starting Interview Coder Frontend (Vite Dev Server)..."

# Ensure we're in the right directory
cd "$(dirname "$0")"

# Kill any existing Vite processes
echo "Cleaning up any existing Vite processes..."
pkill -f "node.*vite" 2>/dev/null || true
sleep 1

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed or not in PATH"
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ] || [ ! -d "node_modules/electron" ]; then
    echo "Dependencies not found. Please run 'bash build.sh' first."
    exit 1
fi

# Start the frontend development server
echo "Starting the frontend development server on http://localhost:5173..."
echo "This will serve the React app that Electron will load."
echo ""
echo "After this starts, run 'bash start-electron.sh' in another terminal."
echo ""
npm run frontend:dev
